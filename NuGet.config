<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <config>
    <add key="repositorypath" value="packages" />
  </config>
  <packageSources>
    <!--To inherit the global NuGet package sources remove the <clear/> line below -->
    <clear />
    <add key="nuget" value="https://api.nuget.org/v3/index.json" />
  </packageSources>
  <disabledPackageSources>
    <!-- Defend against user or machine level disabling of sources that we list in this file. -->
    <clear />
  </disabledPackageSources>
</configuration>
